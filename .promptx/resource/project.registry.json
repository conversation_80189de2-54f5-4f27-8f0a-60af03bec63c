{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-04T07:44:06.318Z", "updatedAt": "2025-08-04T07:44:06.332Z", "resourceCount": 21}, "resources": [{"id": "alex", "source": "project", "protocol": "role", "name": "<PERSON> 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/alex/alex.role.md", "metadata": {"createdAt": "2025-08-04T07:44:06.319Z", "updatedAt": "2025-08-04T07:44:06.319Z", "scannedAt": "2025-08-04T07:44:06.319Z", "path": "role/alex/alex.role.md"}}, {"id": "fastapi-development", "source": "project", "protocol": "execution", "name": "Fastapi Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/alex/execution/fastapi-development.execution.md", "metadata": {"createdAt": "2025-08-04T07:44:06.320Z", "updatedAt": "2025-08-04T07:44:06.320Z", "scannedAt": "2025-08-04T07:44:06.320Z", "path": "role/alex/execution/fastapi-development.execution.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/alex/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T07:44:06.320Z", "updatedAt": "2025-08-04T07:44:06.320Z", "scannedAt": "2025-08-04T07:44:06.320Z", "path": "role/alex/thought/documentation-thinking.thought.md"}}, {"id": "i18n-thinking", "source": "project", "protocol": "thought", "name": "I18n Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/alex/thought/i18n-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T07:44:06.321Z", "updatedAt": "2025-08-04T07:44:06.321Z", "scannedAt": "2025-08-04T07:44:06.321Z", "path": "role/alex/thought/i18n-thinking.thought.md"}}, {"id": "problem-solving", "source": "project", "protocol": "thought", "name": "Problem Solving 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/alex/thought/problem-solving.thought.md", "metadata": {"createdAt": "2025-08-04T07:44:06.321Z", "updatedAt": "2025-08-04T07:44:06.321Z", "scannedAt": "2025-08-04T07:44:06.321Z", "path": "role/alex/thought/problem-solving.thought.md"}}, {"id": "python-senior-thinking", "source": "project", "protocol": "thought", "name": "Python Senior Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/alex/thought/python-senior-thinking.thought.md", "metadata": {"createdAt": "2025-08-04T07:44:06.322Z", "updatedAt": "2025-08-04T07:44:06.322Z", "scannedAt": "2025-08-04T07:44:06.322Z", "path": "role/alex/thought/python-senior-thinking.thought.md"}}, {"id": "research-methodology", "source": "project", "protocol": "execution", "name": "Research Methodology 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pepper/execution/research-methodology.execution.md", "metadata": {"createdAt": "2025-08-04T07:44:06.322Z", "updatedAt": "2025-08-04T07:44:06.322Z", "scannedAt": "2025-08-04T07:44:06.322Z", "path": "role/pepper/execution/research-methodology.execution.md"}}, {"id": "pepper", "source": "project", "protocol": "role", "name": "Pepper 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pepper/pepper.role.md", "metadata": {"createdAt": "2025-08-04T07:44:06.323Z", "updatedAt": "2025-08-04T07:44:06.323Z", "scannedAt": "2025-08-04T07:44:06.323Z", "path": "role/pepper/pepper.role.md"}}, {"id": "deep-research", "source": "project", "protocol": "thought", "name": "Deep Research 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pepper/thought/deep-research.thought.md", "metadata": {"createdAt": "2025-08-04T07:44:06.323Z", "updatedAt": "2025-08-04T07:44:06.323Z", "scannedAt": "2025-08-04T07:44:06.323Z", "path": "role/pepper/thought/deep-research.thought.md"}}, {"id": "python-development-workflow", "source": "project", "protocol": "execution", "name": "Python Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/python-backend-expert/execution/python-development-workflow.execution.md", "metadata": {"createdAt": "2025-08-04T07:44:06.324Z", "updatedAt": "2025-08-04T07:44:06.324Z", "scannedAt": "2025-08-04T07:44:06.324Z", "path": "role/python-backend-expert/execution/python-development-workflow.execution.md"}}, {"id": "python-backend-stack", "source": "project", "protocol": "knowledge", "name": "Python Backend Stack 知识库", "description": "知识库，提供专业知识和信息", "reference": "@project://.promptx/resource/role/python-backend-expert/knowledge/python-backend-stack.knowledge.md", "metadata": {"createdAt": "2025-08-04T07:44:06.325Z", "updatedAt": "2025-08-04T07:44:06.325Z", "scannedAt": "2025-08-04T07:44:06.325Z", "path": "role/python-backend-expert/knowledge/python-backend-stack.knowledge.md"}}, {"id": "python-backend-expert", "source": "project", "protocol": "role", "name": "Python Backend Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/python-backend-expert/python-backend-expert.role.md", "metadata": {"createdAt": "2025-08-04T07:44:06.325Z", "updatedAt": "2025-08-04T07:44:06.325Z", "scannedAt": "2025-08-04T07:44:06.325Z", "path": "role/python-backend-expert/python-backend-expert.role.md"}}, {"id": "python-expertise", "source": "project", "protocol": "thought", "name": "Python Expertise 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/python-backend-expert/thought/python-expertise.thought.md", "metadata": {"createdAt": "2025-08-04T07:44:06.326Z", "updatedAt": "2025-08-04T07:44:06.326Z", "scannedAt": "2025-08-04T07:44:06.326Z", "path": "role/python-backend-expert/thought/python-expertise.thought.md"}}, {"id": "bug-fixer", "source": "project", "protocol": "manual", "name": "Bug Fixer manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/bug-fixer/bug-fixer.manual.md", "metadata": {"createdAt": "2025-08-04T07:44:06.326Z", "updatedAt": "2025-08-04T07:44:06.326Z", "scannedAt": "2025-08-04T07:44:06.326Z", "path": "tool/bug-fixer/bug-fixer.manual.md"}}, {"id": "bug-fixer", "source": "project", "protocol": "tool", "name": "Bug Fixer tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/bug-fixer/bug-fixer.tool.js", "metadata": {"createdAt": "2025-08-04T07:44:06.327Z", "updatedAt": "2025-08-04T07:44:06.327Z", "scannedAt": "2025-08-04T07:44:06.327Z", "path": "tool/bug-fixer/bug-fixer.tool.js"}}, {"id": "crud-generator", "source": "project", "protocol": "manual", "name": "Crud Generator manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/crud-generator/crud-generator.manual.md", "metadata": {"createdAt": "2025-08-04T07:44:06.328Z", "updatedAt": "2025-08-04T07:44:06.328Z", "scannedAt": "2025-08-04T07:44:06.328Z", "path": "tool/crud-generator/crud-generator.manual.md"}}, {"id": "crud-generator", "source": "project", "protocol": "tool", "name": "Crud Generator tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/crud-generator/crud-generator.tool.js", "metadata": {"createdAt": "2025-08-04T07:44:06.329Z", "updatedAt": "2025-08-04T07:44:06.329Z", "scannedAt": "2025-08-04T07:44:06.329Z", "path": "tool/crud-generator/crud-generator.tool.js"}}, {"id": "sql-generator", "source": "project", "protocol": "manual", "name": "Sql Generator manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/sql-generator/sql-generator.manual.md", "metadata": {"createdAt": "2025-08-04T07:44:06.330Z", "updatedAt": "2025-08-04T07:44:06.330Z", "scannedAt": "2025-08-04T07:44:06.330Z", "path": "tool/sql-generator/sql-generator.manual.md"}}, {"id": "sql-generator", "source": "project", "protocol": "tool", "name": "Sql Generator tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/sql-generator/sql-generator.tool.js", "metadata": {"createdAt": "2025-08-04T07:44:06.331Z", "updatedAt": "2025-08-04T07:44:06.331Z", "scannedAt": "2025-08-04T07:44:06.331Z", "path": "tool/sql-generator/sql-generator.tool.js"}}, {"id": "test-generator", "source": "project", "protocol": "manual", "name": "Test Generator manual", "description": "manual类型的资源", "reference": "@project://.promptx/resource/tool/test-generator/test-generator.manual.md", "metadata": {"createdAt": "2025-08-04T07:44:06.331Z", "updatedAt": "2025-08-04T07:44:06.331Z", "scannedAt": "2025-08-04T07:44:06.331Z", "path": "tool/test-generator/test-generator.manual.md"}}, {"id": "test-generator", "source": "project", "protocol": "tool", "name": "Test Generator tool", "description": "tool类型的资源", "reference": "@project://.promptx/resource/tool/test-generator/test-generator.tool.js", "metadata": {"createdAt": "2025-08-04T07:44:06.332Z", "updatedAt": "2025-08-04T07:44:06.332Z", "scannedAt": "2025-08-04T07:44:06.332Z", "path": "tool/test-generator/test-generator.tool.js"}}], "stats": {"totalResources": 21, "byProtocol": {"role": 3, "execution": 3, "thought": 6, "knowledge": 1, "manual": 4, "tool": 4}, "bySource": {"project": 21}}}