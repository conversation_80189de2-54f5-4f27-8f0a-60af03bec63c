import pytest
from starlette.testclient import TestClient


@pytest.mark.parametrize(
    "lang, greeting, use_cv, expected_greeting, expected_message",
    [
        ("en_CA", "Hello, world!", False, "Hello, world!", "success"),
        ("zh-CN, zh", "Hello, world!", False, "你好，世界！", "成功"),
        ("zh", "Hello, world!", False, "你好，世界！", "成功"),
        ("fr-FR", "Hello, world!!", False, "Hello, world!!", "success"),
        ("fr", "Hello, world!", False, "Bonjour le monde!", "succès"),
        ("zh-hant-HK, zh_CN, en-US", "Hello, world!", False, "你好，世界！", "成功"),
        ("zh-CN, zh", "Hello, world!", True, "你好，世界！", "成功"),
    ],
    ids=[
        "test fallback to en",
        "test fallback to zh",
        "test zh",
        "test continuous fallback to en",
        "test fr-FR greeting",
        "test multi-lang and fallback to zh",
        "test context_var",
    ],
)
def test_i18n_endpoint(lang, greeting, use_cv, expected_greeting, expected_message, app_with_i18n_router):
    response = app_with_i18n_router.get(
        "/i18n_test",
        params={"greeting": greeting, "use_context_var": use_cv},
        headers={"Accept-Language": lang},
    )
    assert response.status_code == 200
    assert response.json()["code"] == 1000
    assert response.json()["data"] == expected_greeting
    assert response.json()["message"] == expected_message


@pytest.mark.parametrize("accept_language,expected_pattern", [
    ("zh", "历史保单将于.*到期"),
    ("en", "historical policy will expire"),
    ("fr", "police historique.*expirera"),
])
def test_create_reminder_message_with_language(
    self,
    test_client: TestClient,
    auth_headers,
    accept_language,
    expected_pattern
):
    """测试创建站内信时的多语言支持"""
    headers = {**auth_headers, "Accept-Language": accept_language}

    response = test_client.post(
        "/api/v1/insurance/broker/reminder/message",
        headers=headers,
        json={
            "business_type": "POLICY_RENEWAL",
            "content": "测试消息",
            "receiver_id": 123,
            "sender_id": 456
        }
    )

    assert response.status_code == 200
    # 验证返回的消息内容符合预期语言
    import re
    assert re.search(expected_pattern, response.json()["data"]["content"])
