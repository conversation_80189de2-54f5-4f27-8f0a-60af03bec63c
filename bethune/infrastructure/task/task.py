import json
from abc import ABC
from abc import abstractmethod
from typing import Any

from .implementations import BaseTask
from .messages import InboxMessageMixin
from .registry import initialize_task_registry
from bethune.db.redis import get_redis
from bethune.logging import logger
from bethune.util.date import DateTimeEncoder
from ...model.reminder import BusinessType, ReminderType
from ...util import get_text


class TaskFactory(ABC):
    @abstractmethod
    def create_task(self, task_data: dict[str, Any]) -> "BaseTask":
        pass

    @abstractmethod
    async def consume_task(self, queues: list[str], timeout: int = 5) -> tuple[str, dict[str, Any]] | None:
        pass

    @abstractmethod
    async def produce_task(self, queue: str, task_data: dict[str, Any]):
        pass

    @abstractmethod
    def get_task_queues(self) -> list[str]:
        """获取所有任务队列"""


class RedisTaskFactory(TaskFactory):
    def __init__(self):
        self.redis_conn = get_redis()
        self.task_registry = initialize_task_registry()

    def create_task(self, task_data: dict[str, Any]) -> "BaseTask":
        task_type = f'{task_data["notify_methods"]}_{task_data["reminder_type"]}_{task_data["business_type"]}'
        task_class = self.task_registry.get_task_class(task_type)
        return task_class(task_data)

    async def consume_task(self, queues: list[str], timeout: int = 5):
        tasks = await self.redis_conn.brpop(queues, timeout=timeout)
        if not tasks:
            logger.info(f"{queues} not tasks info.")
            return None
        queue, task_json = tasks
        return queue, json.loads(task_json)

    async def produce_task(self, queue: str, task_data: dict[str, Any]):
        await self.redis_conn.lpush(queue, json.dumps(task_data, cls=DateTimeEncoder, ensure_ascii=False))

    def get_task_queues(self) -> list[str]:
        return self.task_registry.get_queues()
