import gettext
import logging
from pathlib import Path

from fastapi_babel.local_context import context_var

from bethune.settings import settings


def get_text(raw_message: str, language: str = None) -> str:
    if language:
        translation = _get_translation(language)
        return translation.gettext(raw_message)

    try:
        return context_var.get()(raw_message)
    except LookupError:
        default_language = getattr(settings, 'I18N_DEFAULT_LANGUAGE', 'en')
        translation = _get_translation(default_language)
        result = translation.gettext(raw_message)
        if result == raw_message:
            logging.warning(f"Can't find message: {raw_message}")
        return result


_translations = {}


def _get_translation(language: str):
    """获取指定语言的翻译对象"""
    if language not in _translations:
        try:
            locale_dir = Path(settings.I18N_LOCALE_DIR)
            translation = gettext.translation(
                'messages',
                localedir=locale_dir,
                languages=[language],
                fallback=True
            )
            _translations[language] = translation
        except Exception as e:
            logging.warning(f"Failed to load translation for {language}: {e}")
            _translations[language] = gettext.NullTranslations()

    return _translations[language]
